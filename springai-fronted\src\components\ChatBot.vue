<template>
  <div class="chatbot-container">
    <!-- 聊天触发按钮 -->
    <div 
      v-if="!isOpen" 
      class="chat-trigger" 
      @click="toggleChat"
    >
      <t-icon name="chat" size="24px" />
      <div class="chat-badge" v-if="hasNewMessage">
        <span>AI</span>
      </div>
    </div>

    <!-- 聊天窗口 -->
    <div v-if="isOpen" class="chat-window">
      <div class="chat-header">
        <div class="chat-title">
          <t-icon name="chat" />
          <span>AI助手</span>
        </div>
        <t-button 
          theme="default" 
          variant="text" 
          size="small"
          @click="toggleChat"
        >
          <t-icon name="close" />
        </t-button>
      </div>

      <div class="chat-content" ref="chatContentRef">
        <div class="welcome-message" v-if="messages.length === 0">
          <div class="welcome-avatar">
            <t-icon name="chat" size="32px" />
          </div>
          <h3>你好！我是AI助手</h3>
          <p>我可以帮你推荐节目、回答问题或者聊天。有什么我可以帮助你的吗？</p>
        </div>

        <div 
          v-for="(message, index) in messages" 
          :key="index" 
          class="message-item"
          :class="{ 'user-message': message.isUser, 'ai-message': !message.isUser }"
        >
          <div class="message-avatar">
            <t-icon v-if="message.isUser" name="user" />
            <t-icon v-else name="chat" />
          </div>
          <div class="message-content">
            <div class="message-text">{{ message.content }}</div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="message-item ai-message">
          <div class="message-avatar">
            <t-icon name="chat" />
          </div>
          <div class="message-content">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>

      <div class="chat-input">
        <t-input
          v-model="inputMessage"
          placeholder="输入消息..."
          @enter="sendMessage"
          :disabled="isLoading"
        >
          <template #suffix>
            <t-button 
              theme="primary" 
              size="small"
              @click="sendMessage"
              :disabled="isLoading || !inputMessage.trim()"
            >
              <t-icon name="send" />
            </t-button>
          </template>
        </t-input>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { chatApi } from '@/services/api'

interface ChatMessage {
  content: string
  isUser: boolean
  timestamp: Date
}

const isOpen = ref(false)
const inputMessage = ref('')
const messages = ref<ChatMessage[]>([])
const isLoading = ref(false)
const hasNewMessage = ref(false)
const chatContentRef = ref<HTMLElement>()
const chatId = ref(generateChatId())

// 生成聊天会话ID
function generateChatId(): string {
  return 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 切换聊天窗口
const toggleChat = () => {
  isOpen.value = !isOpen.value
  hasNewMessage.value = false
  
  if (isOpen.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}

// 发送消息
const sendMessage = async () => {
  const message = inputMessage.value.trim()
  if (!message || isLoading.value) return

  // 添加用户消息
  messages.value.push({
    content: message,
    isUser: true,
    timestamp: new Date()
  })

  inputMessage.value = ''
  isLoading.value = true

  try {
    // 滚动到底部
    await nextTick()
    scrollToBottom()

    // 调用AI API
    const stream = await chatApi.sendMessage(message, chatId.value)
    
    if (stream) {
      // 创建AI消息对象
      const aiMessage: ChatMessage = {
        content: '',
        isUser: false,
        timestamp: new Date()
      }
      messages.value.push(aiMessage)

      // 处理流式响应
      const reader = stream.getReader()
      const decoder = new TextDecoder()

      try {
        while (true) {
          const { done, value } = await reader.read()
          
          if (done) break

          const chunk = decoder.decode(value, { stream: true })
          aiMessage.content += chunk

          // 实时滚动到底部
          await nextTick()
          scrollToBottom()
        }
      } finally {
        reader.releaseLock()
      }
    }
  } catch (error: any) {
    console.error('发送消息失败:', error)
    MessagePlugin.error('发送消息失败，请稍后重试')
    
    // 添加错误消息
    messages.value.push({
      content: '抱歉，我现在无法回复。请稍后再试。',
      isUser: false,
      timestamp: new Date()
    })
  } finally {
    isLoading.value = false
    await nextTick()
    scrollToBottom()
  }
}

// 滚动到底部
const scrollToBottom = () => {
  if (chatContentRef.value) {
    chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight
  }
}

// 格式化时间
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// 组件挂载时的初始化
onMounted(() => {
  // 可以在这里添加一些初始化逻辑
})
</script>

<style scoped>
.chatbot-container {
  position: fixed;
  bottom: 100px;
  right: 24px;
  z-index: 1000;
}

/* 聊天触发按钮 */
.chat-trigger {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  position: relative;
}

.chat-trigger:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

.chat-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4757;
  color: white;
  border-radius: 12px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: bold;
}

/* 聊天窗口 */
.chat-window {
  width: 380px;
  height: 500px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 聊天头部 */
.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
}

/* 聊天内容区域 */
.chat-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: #f8fafc;
}

/* 欢迎消息 */
.welcome-message {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
}

.welcome-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin: 0 auto 16px;
}

.welcome-message h3 {
  margin: 0 0 8px 0;
  color: #1e293b;
  font-size: 18px;
}

.welcome-message p {
  margin: 0;
  line-height: 1.5;
  font-size: 14px;
}

/* 消息项 */
.message-item {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background: #3b82f6;
  color: white;
}

.ai-message .message-avatar {
  background: #e2e8f0;
  color: #64748b;
}

.message-content {
  max-width: 70%;
}

.user-message .message-content {
  text-align: right;
}

.message-text {
  background: white;
  padding: 12px 16px;
  border-radius: 16px;
  font-size: 14px;
  line-height: 1.4;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-message .message-text {
  background: #3b82f6;
  color: white;
}

.message-time {
  font-size: 11px;
  color: #94a3b8;
  margin-top: 4px;
}

/* 输入状态指示器 */
.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px 16px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: #94a3b8;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 聊天输入 */
.chat-input {
  padding: 16px;
  background: white;
  border-top: 1px solid #e2e8f0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .chatbot-container {
    right: 16px;
    bottom: 90px;
  }

  .chat-window {
    width: calc(100vw - 32px);
    height: 400px;
  }

  .chat-trigger {
    width: 50px;
    height: 50px;
  }
}
</style>
